'use client';

import { useState } from 'react';
import { Search, MapPin, Navigation } from 'lucide-react';
import { Location } from '@/types';

interface SearchBarProps {
  onSearch: (query: string) => void;
  onLocationRequest: () => void;
  currentLocation: Location | null;
}

export default function SearchBar({ onSearch, onLocationRequest, currentLocation }: SearchBarProps) {
  const [query, setQuery] = useState('');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSearch(query);
  };

  return (
    <div className="fixed top-4 right-4 left-16 z-40" dir="rtl">
      <div className="bg-white rounded-lg shadow-lg p-2">
        <form onSubmit={handleSubmit} className="flex items-center gap-2">
          <div className="flex-1 relative">
            <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              value={query}
              onChange={(e) => setQuery(e.target.value)}
              placeholder="ابحث عن حمام أو منطقة..."
              className="w-full pr-10 pl-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
          
          <button
            type="submit"
            className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors"
          >
            بحث
          </button>
          
          <button
            type="button"
            onClick={onLocationRequest}
            className={`p-2 rounded-lg transition-colors ${
              currentLocation 
                ? 'bg-green-500 hover:bg-green-600 text-white' 
                : 'bg-gray-100 hover:bg-gray-200 text-gray-600'
            }`}
            title="تحديد موقعي الحالي"
          >
            <Navigation className="w-5 h-5" />
          </button>
        </form>
        
        {/* Quick suggestions */}
        <div className="mt-2 flex flex-wrap gap-2">
          {['مولات', 'مطاعم', 'محطات وقود', 'حمامات عامة'].map((suggestion) => (
            <button
              key={suggestion}
              onClick={() => {
                setQuery(suggestion);
                onSearch(suggestion);
              }}
              className="text-xs bg-gray-100 hover:bg-gray-200 text-gray-700 px-3 py-1 rounded-full transition-colors"
            >
              {suggestion}
            </button>
          ))}
        </div>
      </div>
    </div>
  );
}

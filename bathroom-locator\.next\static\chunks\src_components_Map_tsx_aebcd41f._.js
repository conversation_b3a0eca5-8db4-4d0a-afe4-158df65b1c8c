(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/components/Map.tsx [app-client] (ecmascript, next/dynamic entry, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_bc589b58._.js",
  "static/chunks/src_components_Map_tsx_80bcc3a8._.js",
  "static/chunks/src_components_Map_tsx_10c62d93._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/components/Map.tsx [app-client] (ecmascript, next/dynamic entry)");
    });
});
}}),
}]);
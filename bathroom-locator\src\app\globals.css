@import "tailwindcss";
@import "leaflet/dist/leaflet.css";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

/* Custom styles for leaflet map */
.leaflet-container {
  height: 100vh;
  width: 100%;
}

/* Custom marker styles */
.custom-marker {
  background: transparent !important;
  border: none !important;
}

/* RTL support for Arabic text */
[dir="rtl"] .leaflet-popup-content {
  text-align: right;
}

/* Fix for popup positioning in RTL */
[dir="rtl"] .leaflet-popup-tip-container {
  left: auto;
  right: 50%;
  margin-right: -5px;
}

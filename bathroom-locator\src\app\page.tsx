'use client';

import { useState, useEffect } from 'react';
import dynamic from 'next/dynamic';
import { Bathroom, Location, MapFilters } from '@/types';
import { mockBathrooms } from '@/data/mockData';
import SearchBar from '@/components/SearchBar';
import Filters from '@/components/Filters';
import BathroomList from '@/components/BathroomList';
import { Plus, MapPin } from 'lucide-react';

// Dynamically import Map component to avoid SSR issues
const Map = dynamic(() => import('@/components/Map'), {
  ssr: false,
  loading: () => (
    <div className="w-full h-full bg-gray-200 flex items-center justify-center">
      <div className="text-gray-600">جاري تحميل الخريطة...</div>
    </div>
  ),
});

export default function Home() {
  const [bathrooms, setBathrooms] = useState<Bathroom[]>(mockBathrooms);
  const [filteredBathrooms, setFilteredBathrooms] = useState<Bathroom[]>(mockBathrooms);
  const [currentLocation, setCurrentLocation] = useState<Location | null>(null);
  const [selectedBathroom, setSelectedBathroom] = useState<Bathroom | null>(null);
  const [isFiltersOpen, setIsFiltersOpen] = useState(false);
  const [isBathroomListOpen, setIsBathroomListOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  const [filters, setFilters] = useState<MapFilters>({
    type: [],
    isFree: null,
    hasDisabledAccess: null,
    minRating: 0,
    maxDistance: 10,
  });

  // Default center (Riyadh)
  const [mapCenter, setMapCenter] = useState<Location>({
    latitude: 24.7136,
    longitude: 46.6753,
  });

  // Get user's current location
  const getCurrentLocation = () => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const location = {
            latitude: position.coords.latitude,
            longitude: position.coords.longitude,
          };
          setCurrentLocation(location);
          setMapCenter(location);
        },
        (error) => {
          console.error('Error getting location:', error);
          alert('لا يمكن الحصول على موقعك الحالي. تأكد من تفعيل خدمات الموقع.');
        }
      );
    } else {
      alert('متصفحك لا يدعم خدمات تحديد الموقع.');
    }
  };

  // Filter bathrooms based on current filters and search query
  useEffect(() => {
    let filtered = bathrooms;

    // Apply search filter
    if (searchQuery.trim()) {
      filtered = filtered.filter(bathroom =>
        bathroom.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        bathroom.address.toLowerCase().includes(searchQuery.toLowerCase()) ||
        bathroom.type.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Apply type filter
    if (filters.type.length > 0) {
      filtered = filtered.filter(bathroom => filters.type.includes(bathroom.type));
    }

    // Apply free/paid filter
    if (filters.isFree !== null) {
      filtered = filtered.filter(bathroom => bathroom.isFree === filters.isFree);
    }

    // Apply accessibility filter
    if (filters.hasDisabledAccess !== null) {
      filtered = filtered.filter(bathroom => bathroom.hasDisabledAccess === filters.hasDisabledAccess);
    }

    // Apply rating filter
    filtered = filtered.filter(bathroom => bathroom.rating >= filters.minRating);

    // Apply distance filter (if current location is available)
    if (currentLocation) {
      filtered = filtered.filter(bathroom => {
        const distance = calculateDistance(
          currentLocation.latitude,
          currentLocation.longitude,
          bathroom.latitude,
          bathroom.longitude
        );
        return distance <= filters.maxDistance;
      });
    }

    setFilteredBathrooms(filtered);
  }, [bathrooms, filters, searchQuery, currentLocation]);

  // Calculate distance between two points
  const calculateDistance = (lat1: number, lon1: number, lat2: number, lon2: number): number => {
    const R = 6371; // Radius of the Earth in kilometers
    const dLat = (lat2 - lat1) * Math.PI / 180;
    const dLon = (lon2 - lon1) * Math.PI / 180;
    const a =
      Math.sin(dLat/2) * Math.sin(dLat/2) +
      Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
      Math.sin(dLon/2) * Math.sin(dLon/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    const d = R * c; // Distance in kilometers
    return d;
  };

  const handleBathroomSelect = (bathroom: Bathroom) => {
    setSelectedBathroom(bathroom);
    setMapCenter({ latitude: bathroom.latitude, longitude: bathroom.longitude });
  };

  const handleSearch = (query: string) => {
    setSearchQuery(query);
  };

  return (
    <div className="h-screen w-full relative overflow-hidden">
      {/* Map Container */}
      <div className="absolute inset-0">
        <Map
          bathrooms={filteredBathrooms}
          center={mapCenter}
          onBathroomSelect={handleBathroomSelect}
        />
      </div>

      {/* Search Bar */}
      <SearchBar
        onSearch={handleSearch}
        onLocationRequest={getCurrentLocation}
        currentLocation={currentLocation}
      />

      {/* Filters */}
      <Filters
        filters={filters}
        onFiltersChange={setFilters}
        isOpen={isFiltersOpen}
        onToggle={() => setIsFiltersOpen(!isFiltersOpen)}
      />

      {/* Bathroom List */}
      <BathroomList
        bathrooms={filteredBathrooms}
        onBathroomSelect={handleBathroomSelect}
        currentLocation={currentLocation}
        isOpen={isBathroomListOpen}
        onToggle={() => setIsBathroomListOpen(!isBathroomListOpen)}
      />

      {/* Add Bathroom Button */}
      <button
        className="fixed bottom-4 right-4 z-50 bg-blue-500 hover:bg-blue-600 text-white rounded-full p-4 shadow-lg transition-colors"
        title="إضافة حمام جديد"
        onClick={() => alert('ميزة إضافة حمام جديد ستكون متاحة قريباً!')}
      >
        <Plus className="w-6 h-6" />
      </button>

      {/* Status Bar */}
      {currentLocation && (
        <div className="fixed bottom-4 left-1/2 transform -translate-x-1/2 z-40 bg-white rounded-lg shadow-lg px-4 py-2 text-sm">
          <div className="flex items-center text-green-600">
            <MapPin className="w-4 h-4 ml-1" />
            <span>تم تحديد موقعك - عرض {filteredBathrooms.length} حمام</span>
          </div>
        </div>
      )}
    </div>
  );
}

'use client';

import { useState } from 'react';
import { MapFilters } from '@/types';
import { Filter, X, Star, DollarSign, Accessibility } from 'lucide-react';

interface FiltersProps {
  filters: MapFilters;
  onFiltersChange: (filters: MapFilters) => void;
  isOpen: boolean;
  onToggle: () => void;
}

export default function Filters({ filters, onFiltersChange, isOpen, onToggle }: FiltersProps) {
  const bathroomTypes = [
    { value: 'public', label: 'عام' },
    { value: 'private', label: 'خاص' },
    { value: 'restaurant', label: 'مطعم' },
    { value: 'mall', label: 'مول' },
    { value: 'gas_station', label: 'محطة وقود' },
  ];

  const handleTypeChange = (type: string, checked: boolean) => {
    const newTypes = checked
      ? [...filters.type, type]
      : filters.type.filter(t => t !== type);
    
    onFiltersChange({ ...filters, type: newTypes });
  };

  const resetFilters = () => {
    onFiltersChange({
      type: [],
      isFree: null,
      hasDisabledAccess: null,
      minRating: 0,
      maxDistance: 10,
    });
  };

  return (
    <>
      {/* Filter Toggle Button */}
      <button
        onClick={onToggle}
        className="fixed top-4 left-4 z-50 bg-white rounded-lg shadow-lg p-3 hover:bg-gray-50 transition-colors"
      >
        <Filter className="w-5 h-5" />
      </button>

      {/* Filter Panel */}
      {isOpen && (
        <div className="fixed inset-0 z-40 bg-black bg-opacity-50" onClick={onToggle}>
          <div 
            className="fixed right-0 top-0 h-full w-80 bg-white shadow-xl overflow-y-auto"
            onClick={(e) => e.stopPropagation()}
            dir="rtl"
          >
            <div className="p-4">
              {/* Header */}
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-bold">الفلاتر</h2>
                <button
                  onClick={onToggle}
                  className="p-2 hover:bg-gray-100 rounded-lg"
                >
                  <X className="w-5 h-5" />
                </button>
              </div>

              {/* Bathroom Types */}
              <div className="mb-6">
                <h3 className="font-semibold mb-3">نوع الحمام</h3>
                <div className="space-y-2">
                  {bathroomTypes.map((type) => (
                    <label key={type.value} className="flex items-center">
                      <input
                        type="checkbox"
                        checked={filters.type.includes(type.value)}
                        onChange={(e) => handleTypeChange(type.value, e.target.checked)}
                        className="ml-2 rounded"
                      />
                      <span>{type.label}</span>
                    </label>
                  ))}
                </div>
              </div>

              {/* Free/Paid */}
              <div className="mb-6">
                <h3 className="font-semibold mb-3 flex items-center">
                  <DollarSign className="w-4 h-4 ml-2" />
                  الرسوم
                </h3>
                <div className="space-y-2">
                  <label className="flex items-center">
                    <input
                      type="radio"
                      name="isFree"
                      checked={filters.isFree === null}
                      onChange={() => onFiltersChange({ ...filters, isFree: null })}
                      className="ml-2"
                    />
                    <span>الكل</span>
                  </label>
                  <label className="flex items-center">
                    <input
                      type="radio"
                      name="isFree"
                      checked={filters.isFree === true}
                      onChange={() => onFiltersChange({ ...filters, isFree: true })}
                      className="ml-2"
                    />
                    <span>مجاني فقط</span>
                  </label>
                  <label className="flex items-center">
                    <input
                      type="radio"
                      name="isFree"
                      checked={filters.isFree === false}
                      onChange={() => onFiltersChange({ ...filters, isFree: false })}
                      className="ml-2"
                    />
                    <span>مدفوع فقط</span>
                  </label>
                </div>
              </div>

              {/* Accessibility */}
              <div className="mb-6">
                <h3 className="font-semibold mb-3 flex items-center">
                  <Accessibility className="w-4 h-4 ml-2" />
                  إمكانية الوصول
                </h3>
                <div className="space-y-2">
                  <label className="flex items-center">
                    <input
                      type="radio"
                      name="hasDisabledAccess"
                      checked={filters.hasDisabledAccess === null}
                      onChange={() => onFiltersChange({ ...filters, hasDisabledAccess: null })}
                      className="ml-2"
                    />
                    <span>الكل</span>
                  </label>
                  <label className="flex items-center">
                    <input
                      type="radio"
                      name="hasDisabledAccess"
                      checked={filters.hasDisabledAccess === true}
                      onChange={() => onFiltersChange({ ...filters, hasDisabledAccess: true })}
                      className="ml-2"
                    />
                    <span>مناسب لذوي الاحتياجات فقط</span>
                  </label>
                </div>
              </div>

              {/* Rating */}
              <div className="mb-6">
                <h3 className="font-semibold mb-3 flex items-center">
                  <Star className="w-4 h-4 ml-2" />
                  التقييم الأدنى
                </h3>
                <input
                  type="range"
                  min="0"
                  max="5"
                  step="0.5"
                  value={filters.minRating}
                  onChange={(e) => onFiltersChange({ ...filters, minRating: parseFloat(e.target.value) })}
                  className="w-full"
                />
                <div className="flex justify-between text-sm text-gray-600 mt-1">
                  <span>0</span>
                  <span className="font-medium">{filters.minRating}</span>
                  <span>5</span>
                </div>
              </div>

              {/* Distance */}
              <div className="mb-6">
                <h3 className="font-semibold mb-3">المسافة القصوى (كم)</h3>
                <input
                  type="range"
                  min="1"
                  max="50"
                  value={filters.maxDistance}
                  onChange={(e) => onFiltersChange({ ...filters, maxDistance: parseInt(e.target.value) })}
                  className="w-full"
                />
                <div className="flex justify-between text-sm text-gray-600 mt-1">
                  <span>1</span>
                  <span className="font-medium">{filters.maxDistance} كم</span>
                  <span>50</span>
                </div>
              </div>

              {/* Reset Button */}
              <button
                onClick={resetFilters}
                className="w-full bg-gray-100 hover:bg-gray-200 text-gray-800 py-2 px-4 rounded-lg transition-colors"
              >
                إعادة تعيين الفلاتر
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
}

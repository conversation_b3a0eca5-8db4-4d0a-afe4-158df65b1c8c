'use client';

import { useEffect, useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, useMap } from 'react-leaflet';
import L from 'leaflet';
import { Bathroom, Location } from '@/types';
import { MapPin, Star, Clock, DollarSign, Accessibility } from 'lucide-react';

// Fix for default markers in react-leaflet
delete (L.Icon.Default.prototype as any)._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
});

interface MapProps {
  bathrooms: Bathroom[];
  center: Location;
  onBathroomSelect: (bathroom: Bathroom) => void;
}

// Custom icon for different bathroom types
const createCustomIcon = (type: string, isClean: boolean) => {
  const color = isClean ? '#10b981' : '#ef4444';
  const iconHtml = `
    <div style="
      background-color: ${color};
      width: 30px;
      height: 30px;
      border-radius: 50%;
      border: 3px solid white;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 2px 4px rgba(0,0,0,0.3);
    ">
      <span style="color: white; font-size: 16px;">🚻</span>
    </div>
  `;
  
  return L.divIcon({
    html: iconHtml,
    className: 'custom-marker',
    iconSize: [30, 30],
    iconAnchor: [15, 15],
  });
};

// Component to handle map centering
function MapCenter({ center }: { center: Location }) {
  const map = useMap();
  
  useEffect(() => {
    map.setView([center.latitude, center.longitude], map.getZoom());
  }, [center, map]);
  
  return null;
}

export default function Map({ bathrooms, center, onBathroomSelect }: MapProps) {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  if (!isClient) {
    return (
      <div className="w-full h-full bg-gray-200 flex items-center justify-center">
        <div className="text-gray-600">جاري تحميل الخريطة...</div>
      </div>
    );
  }

  const getTypeLabel = (type: string) => {
    const labels = {
      public: 'عام',
      private: 'خاص',
      restaurant: 'مطعم',
      mall: 'مول',
      gas_station: 'محطة وقود'
    };
    return labels[type as keyof typeof labels] || type;
  };

  return (
    <MapContainer
      center={[center.latitude, center.longitude]}
      zoom={13}
      style={{ height: '100%', width: '100%' }}
      className="z-0"
    >
      <TileLayer
        attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
        url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
      />
      
      <MapCenter center={center} />
      
      {bathrooms.map((bathroom) => (
        <Marker
          key={bathroom.id}
          position={[bathroom.latitude, bathroom.longitude]}
          icon={createCustomIcon(bathroom.type, bathroom.isClean)}
          eventHandlers={{
            click: () => onBathroomSelect(bathroom),
          }}
        >
          <Popup>
            <div className="p-2 min-w-[200px]" dir="rtl">
              <h3 className="font-bold text-lg mb-2">{bathroom.name}</h3>
              <p className="text-sm text-gray-600 mb-2">{bathroom.address}</p>
              
              <div className="flex items-center gap-2 mb-2">
                <Star className="w-4 h-4 text-yellow-500" />
                <span className="text-sm">{bathroom.rating}/5</span>
              </div>
              
              <div className="flex items-center gap-2 mb-2">
                <Clock className="w-4 h-4 text-blue-500" />
                <span className="text-sm">{bathroom.openingHours}</span>
              </div>
              
              <div className="flex gap-2 mb-2">
                {bathroom.isFree && (
                  <span className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded">
                    مجاني
                  </span>
                )}
                {bathroom.hasDisabledAccess && (
                  <span className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">
                    <Accessibility className="w-3 h-3 inline ml-1" />
                    مناسب لذوي الاحتياجات
                  </span>
                )}
                {bathroom.isClean && (
                  <span className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded">
                    نظيف
                  </span>
                )}
              </div>
              
              <div className="text-xs text-gray-500">
                النوع: {getTypeLabel(bathroom.type)}
              </div>
            </div>
          </Popup>
        </Marker>
      ))}
    </MapContainer>
  );
}

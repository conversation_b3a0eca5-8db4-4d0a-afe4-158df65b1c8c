'use client';

import { useState } from 'react';
import { Bathroom, Location } from '@/types';
import { 
  List, 
  X, 
  Star, 
  Clock, 
  MapPin, 
  DollarSign, 
  Accessibility, 
  Baby,
  Navigation,
  Phone
} from 'lucide-react';

interface BathroomListProps {
  bathrooms: Bathroom[];
  onBathroomSelect: (bathroom: Bathroom) => void;
  currentLocation: Location | null;
  isOpen: boolean;
  onToggle: () => void;
}

// Function to calculate distance between two points
function calculateDistance(lat1: number, lon1: number, lat2: number, lon2: number): number {
  const R = 6371; // Radius of the Earth in kilometers
  const dLat = (lat2 - lat1) * Math.PI / 180;
  const dLon = (lon2 - lon1) * Math.PI / 180;
  const a = 
    Math.sin(dLat/2) * Math.sin(dLat/2) +
    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) * 
    Math.sin(dLon/2) * Math.sin(dLon/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
  const d = R * c; // Distance in kilometers
  return Math.round(d * 10) / 10; // Round to 1 decimal place
}

export default function BathroomList({ 
  bathrooms, 
  onBathroomSelect, 
  currentLocation, 
  isOpen, 
  onToggle 
}: BathroomListProps) {
  const getTypeLabel = (type: string) => {
    const labels = {
      public: 'عام',
      private: 'خاص',
      restaurant: 'مطعم',
      mall: 'مول',
      gas_station: 'محطة وقود'
    };
    return labels[type as keyof typeof labels] || type;
  };

  const getDirections = (bathroom: Bathroom) => {
    const url = `https://www.google.com/maps/dir/?api=1&destination=${bathroom.latitude},${bathroom.longitude}`;
    window.open(url, '_blank');
  };

  // Sort bathrooms by distance if current location is available
  const sortedBathrooms = currentLocation 
    ? [...bathrooms].sort((a, b) => {
        const distanceA = calculateDistance(
          currentLocation.latitude, 
          currentLocation.longitude, 
          a.latitude, 
          a.longitude
        );
        const distanceB = calculateDistance(
          currentLocation.latitude, 
          currentLocation.longitude, 
          b.latitude, 
          b.longitude
        );
        return distanceA - distanceB;
      })
    : bathrooms;

  return (
    <>
      {/* List Toggle Button */}
      <button
        onClick={onToggle}
        className="fixed bottom-4 left-4 z-50 bg-white rounded-lg shadow-lg p-3 hover:bg-gray-50 transition-colors"
      >
        <List className="w-5 h-5" />
      </button>

      {/* List Panel */}
      {isOpen && (
        <div className="fixed inset-0 z-40 bg-black bg-opacity-50" onClick={onToggle}>
          <div 
            className="fixed left-0 top-0 h-full w-96 bg-white shadow-xl overflow-y-auto"
            onClick={(e) => e.stopPropagation()}
            dir="rtl"
          >
            <div className="p-4">
              {/* Header */}
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-xl font-bold">
                  الحمامات القريبة ({bathrooms.length})
                </h2>
                <button
                  onClick={onToggle}
                  className="p-2 hover:bg-gray-100 rounded-lg"
                >
                  <X className="w-5 h-5" />
                </button>
              </div>

              {/* Bathroom Cards */}
              <div className="space-y-4">
                {sortedBathrooms.map((bathroom) => {
                  const distance = currentLocation 
                    ? calculateDistance(
                        currentLocation.latitude,
                        currentLocation.longitude,
                        bathroom.latitude,
                        bathroom.longitude
                      )
                    : null;

                  return (
                    <div
                      key={bathroom.id}
                      className="bg-gray-50 rounded-lg p-4 hover:bg-gray-100 transition-colors cursor-pointer"
                      onClick={() => onBathroomSelect(bathroom)}
                    >
                      {/* Header */}
                      <div className="flex items-start justify-between mb-2">
                        <div className="flex-1">
                          <h3 className="font-semibold text-lg">{bathroom.name}</h3>
                          <p className="text-sm text-gray-600 flex items-center">
                            <MapPin className="w-3 h-3 ml-1" />
                            {bathroom.address}
                          </p>
                        </div>
                        {distance && (
                          <span className="text-sm font-medium text-blue-600 bg-blue-100 px-2 py-1 rounded">
                            {distance} كم
                          </span>
                        )}
                      </div>

                      {/* Rating and Type */}
                      <div className="flex items-center gap-4 mb-3">
                        <div className="flex items-center">
                          <Star className="w-4 h-4 text-yellow-500 ml-1" />
                          <span className="text-sm font-medium">{bathroom.rating}</span>
                        </div>
                        <span className="text-sm bg-gray-200 text-gray-700 px-2 py-1 rounded">
                          {getTypeLabel(bathroom.type)}
                        </span>
                      </div>

                      {/* Features */}
                      <div className="flex flex-wrap gap-2 mb-3">
                        {bathroom.isFree && (
                          <span className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded flex items-center">
                            <DollarSign className="w-3 h-3 ml-1" />
                            مجاني
                          </span>
                        )}
                        {bathroom.hasDisabledAccess && (
                          <span className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded flex items-center">
                            <Accessibility className="w-3 h-3 ml-1" />
                            مناسب لذوي الاحتياجات
                          </span>
                        )}
                        {bathroom.hasChangingTable && (
                          <span className="bg-purple-100 text-purple-800 text-xs px-2 py-1 rounded flex items-center">
                            <Baby className="w-3 h-3 ml-1" />
                            طاولة تغيير
                          </span>
                        )}
                        {bathroom.isClean && (
                          <span className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded">
                            نظيف
                          </span>
                        )}
                      </div>

                      {/* Opening Hours */}
                      <div className="flex items-center text-sm text-gray-600 mb-3">
                        <Clock className="w-4 h-4 ml-1" />
                        <span>{bathroom.openingHours}</span>
                      </div>

                      {/* Actions */}
                      <div className="flex gap-2">
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            getDirections(bathroom);
                          }}
                          className="flex-1 bg-blue-500 hover:bg-blue-600 text-white text-sm py-2 px-3 rounded flex items-center justify-center transition-colors"
                        >
                          <Navigation className="w-4 h-4 ml-1" />
                          الاتجاهات
                        </button>
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            onBathroomSelect(bathroom);
                            onToggle();
                          }}
                          className="flex-1 bg-gray-200 hover:bg-gray-300 text-gray-800 text-sm py-2 px-3 rounded transition-colors"
                        >
                          عرض على الخريطة
                        </button>
                      </div>
                    </div>
                  );
                })}
              </div>

              {bathrooms.length === 0 && (
                <div className="text-center py-8 text-gray-500">
                  <MapPin className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                  <p>لا توجد حمامات تطابق معايير البحث</p>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </>
  );
}

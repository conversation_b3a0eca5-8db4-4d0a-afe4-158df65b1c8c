{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/murafiq/bathroom-locator/src/components/Map.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState } from 'react';\nimport { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, useMap } from 'react-leaflet';\nimport L from 'leaflet';\nimport { Bathroom, Location } from '@/types';\nimport { MapPin, Star, Clock, DollarSign, Accessibility } from 'lucide-react';\n\n// Fix for default markers in react-leaflet\ndelete (L.Icon.Default.prototype as any)._getIconUrl;\nL.Icon.Default.mergeOptions({\n  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',\n  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',\n  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',\n});\n\ninterface MapProps {\n  bathrooms: Bathroom[];\n  center: Location;\n  onBathroomSelect: (bathroom: Bathroom) => void;\n}\n\n// Custom icon for different bathroom types\nconst createCustomIcon = (type: string, isClean: boolean) => {\n  const color = isClean ? '#10b981' : '#ef4444';\n  const iconHtml = `\n    <div style=\"\n      background-color: ${color};\n      width: 30px;\n      height: 30px;\n      border-radius: 50%;\n      border: 3px solid white;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      box-shadow: 0 2px 4px rgba(0,0,0,0.3);\n    \">\n      <span style=\"color: white; font-size: 16px;\">🚻</span>\n    </div>\n  `;\n  \n  return L.divIcon({\n    html: iconHtml,\n    className: 'custom-marker',\n    iconSize: [30, 30],\n    iconAnchor: [15, 15],\n  });\n};\n\n// Component to handle map centering\nfunction MapCenter({ center }: { center: Location }) {\n  const map = useMap();\n  \n  useEffect(() => {\n    map.setView([center.latitude, center.longitude], map.getZoom());\n  }, [center, map]);\n  \n  return null;\n}\n\nexport default function Map({ bathrooms, center, onBathroomSelect }: MapProps) {\n  const [isClient, setIsClient] = useState(false);\n\n  useEffect(() => {\n    setIsClient(true);\n  }, []);\n\n  if (!isClient) {\n    return (\n      <div className=\"w-full h-full bg-gray-200 flex items-center justify-center\">\n        <div className=\"text-gray-600\">جاري تحميل الخريطة...</div>\n      </div>\n    );\n  }\n\n  const getTypeLabel = (type: string) => {\n    const labels = {\n      public: 'عام',\n      private: 'خاص',\n      restaurant: 'مطعم',\n      mall: 'مول',\n      gas_station: 'محطة وقود'\n    };\n    return labels[type as keyof typeof labels] || type;\n  };\n\n  return (\n    <MapContainer\n      center={[center.latitude, center.longitude]}\n      zoom={13}\n      style={{ height: '100%', width: '100%' }}\n      className=\"z-0\"\n    >\n      <TileLayer\n        attribution='&copy; <a href=\"https://www.openstreetmap.org/copyright\">OpenStreetMap</a> contributors'\n        url=\"https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png\"\n      />\n      \n      <MapCenter center={center} />\n      \n      {bathrooms.map((bathroom) => (\n        <Marker\n          key={bathroom.id}\n          position={[bathroom.latitude, bathroom.longitude]}\n          icon={createCustomIcon(bathroom.type, bathroom.isClean)}\n          eventHandlers={{\n            click: () => onBathroomSelect(bathroom),\n          }}\n        >\n          <Popup>\n            <div className=\"p-2 min-w-[200px]\" dir=\"rtl\">\n              <h3 className=\"font-bold text-lg mb-2\">{bathroom.name}</h3>\n              <p className=\"text-sm text-gray-600 mb-2\">{bathroom.address}</p>\n              \n              <div className=\"flex items-center gap-2 mb-2\">\n                <Star className=\"w-4 h-4 text-yellow-500\" />\n                <span className=\"text-sm\">{bathroom.rating}/5</span>\n              </div>\n              \n              <div className=\"flex items-center gap-2 mb-2\">\n                <Clock className=\"w-4 h-4 text-blue-500\" />\n                <span className=\"text-sm\">{bathroom.openingHours}</span>\n              </div>\n              \n              <div className=\"flex gap-2 mb-2\">\n                {bathroom.isFree && (\n                  <span className=\"bg-green-100 text-green-800 text-xs px-2 py-1 rounded\">\n                    مجاني\n                  </span>\n                )}\n                {bathroom.hasDisabledAccess && (\n                  <span className=\"bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded\">\n                    <Accessibility className=\"w-3 h-3 inline ml-1\" />\n                    مناسب لذوي الاحتياجات\n                  </span>\n                )}\n                {bathroom.isClean && (\n                  <span className=\"bg-green-100 text-green-800 text-xs px-2 py-1 rounded\">\n                    نظيف\n                  </span>\n                )}\n              </div>\n              \n              <div className=\"text-xs text-gray-500\">\n                النوع: {getTypeLabel(bathroom.type)}\n              </div>\n            </div>\n          </Popup>\n        </Marker>\n      ))}\n    </MapContainer>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAEA;AAAA;AAAA;;;AANA;;;;;AAQA,2CAA2C;AAC3C,OAAO,AAAC,oJAAA,CAAA,UAAC,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAS,WAAW;AACpD,oJAAA,CAAA,UAAC,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC;IAC1B,eAAe;IACf,SAAS;IACT,WAAW;AACb;AAQA,2CAA2C;AAC3C,MAAM,mBAAmB,CAAC,MAAc;IACtC,MAAM,QAAQ,UAAU,YAAY;IACpC,MAAM,WAAW,CAAC;;wBAEI,EAAE,MAAM;;;;;;;;;;;;EAY9B,CAAC;IAED,OAAO,oJAAA,CAAA,UAAC,CAAC,OAAO,CAAC;QACf,MAAM;QACN,WAAW;QACX,UAAU;YAAC;YAAI;SAAG;QAClB,YAAY;YAAC;YAAI;SAAG;IACtB;AACF;AAEA,oCAAoC;AACpC,SAAS,UAAU,EAAE,MAAM,EAAwB;;IACjD,MAAM,MAAM,CAAA,GAAA,mJAAA,CAAA,SAAM,AAAD;IAEjB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,IAAI,OAAO,CAAC;gBAAC,OAAO,QAAQ;gBAAE,OAAO,SAAS;aAAC,EAAE,IAAI,OAAO;QAC9D;8BAAG;QAAC;QAAQ;KAAI;IAEhB,OAAO;AACT;GARS;;QACK,mJAAA,CAAA,SAAM;;;KADX;AAUM,SAAS,IAAI,EAAE,SAAS,EAAE,MAAM,EAAE,gBAAgB,EAAY;;IAC3E,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;yBAAE;YACR,YAAY;QACd;wBAAG,EAAE;IAEL,IAAI,CAAC,UAAU;QACb,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;0BAAgB;;;;;;;;;;;IAGrC;IAEA,MAAM,eAAe,CAAC;QACpB,MAAM,SAAS;YACb,QAAQ;YACR,SAAS;YACT,YAAY;YACZ,MAAM;YACN,aAAa;QACf;QACA,OAAO,MAAM,CAAC,KAA4B,IAAI;IAChD;IAEA,qBACE,6LAAC,0JAAA,CAAA,eAAY;QACX,QAAQ;YAAC,OAAO,QAAQ;YAAE,OAAO,SAAS;SAAC;QAC3C,MAAM;QACN,OAAO;YAAE,QAAQ;YAAQ,OAAO;QAAO;QACvC,WAAU;;0BAEV,6LAAC,uJAAA,CAAA,YAAS;gBACR,aAAY;gBACZ,KAAI;;;;;;0BAGN,6LAAC;gBAAU,QAAQ;;;;;;YAElB,UAAU,GAAG,CAAC,CAAC,yBACd,6LAAC,oJAAA,CAAA,SAAM;oBAEL,UAAU;wBAAC,SAAS,QAAQ;wBAAE,SAAS,SAAS;qBAAC;oBACjD,MAAM,iBAAiB,SAAS,IAAI,EAAE,SAAS,OAAO;oBACtD,eAAe;wBACb,OAAO,IAAM,iBAAiB;oBAChC;8BAEA,cAAA,6LAAC,mJAAA,CAAA,QAAK;kCACJ,cAAA,6LAAC;4BAAI,WAAU;4BAAoB,KAAI;;8CACrC,6LAAC;oCAAG,WAAU;8CAA0B,SAAS,IAAI;;;;;;8CACrD,6LAAC;oCAAE,WAAU;8CAA8B,SAAS,OAAO;;;;;;8CAE3D,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;sDAChB,6LAAC;4CAAK,WAAU;;gDAAW,SAAS,MAAM;gDAAC;;;;;;;;;;;;;8CAG7C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,6LAAC;4CAAK,WAAU;sDAAW,SAAS,YAAY;;;;;;;;;;;;8CAGlD,6LAAC;oCAAI,WAAU;;wCACZ,SAAS,MAAM,kBACd,6LAAC;4CAAK,WAAU;sDAAwD;;;;;;wCAIzE,SAAS,iBAAiB,kBACzB,6LAAC;4CAAK,WAAU;;8DACd,6LAAC,uNAAA,CAAA,gBAAa;oDAAC,WAAU;;;;;;gDAAwB;;;;;;;wCAIpD,SAAS,OAAO,kBACf,6LAAC;4CAAK,WAAU;sDAAwD;;;;;;;;;;;;8CAM5E,6LAAC;oCAAI,WAAU;;wCAAwB;wCAC7B,aAAa,SAAS,IAAI;;;;;;;;;;;;;;;;;;mBA1CnC,SAAS,EAAE;;;;;;;;;;;AAkD1B;IA5FwB;MAAA", "debugId": null}}]}
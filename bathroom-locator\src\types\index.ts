export interface Bathroom {
  id: string;
  name: string;
  latitude: number;
  longitude: number;
  address: string;
  type: 'public' | 'private' | 'restaurant' | 'mall' | 'gas_station';
  isFree: boolean;
  isClean: boolean;
  hasDisabledAccess: boolean;
  hasChangingTable: boolean;
  openingHours: string;
  rating: number;
  reviews: Review[];
  addedBy: string;
  addedAt: Date;
  lastUpdated: Date;
  isVerified: boolean;
}

export interface Review {
  id: string;
  bathroomId: string;
  userId: string;
  userName: string;
  rating: number;
  comment: string;
  cleanliness: number;
  accessibility: number;
  createdAt: Date;
}

export interface User {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  joinedAt: Date;
  contributionsCount: number;
}

export interface MapFilters {
  type: string[];
  isFree: boolean | null;
  hasDisabledAccess: boolean | null;
  minRating: number;
  maxDistance: number; // in kilometers
}

export interface Location {
  latitude: number;
  longitude: number;
}
